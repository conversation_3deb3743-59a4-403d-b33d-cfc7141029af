"use client";

import { motion, useTransform } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Simple train animation - cards just slide from right to left
  const createCardAnimation = (index) => {
    // Cards start moving after 30% delay to let title stay visible
    const delayOffset = 0.3;

    // Each card has a gap between them (spacing)
    const cardSpacing = 350; // pixels between cards
    const startPosition = 800 + (index * cardSpacing); // Start off-screen to the right

    // Simple horizontal movement from right to left
    const translateX = useTransform(scrollProgress,
      [delayOffset, 1.0],
      [startPosition, -1200] // Move from right side to left side
    );

    return { translateX };
  };

  return (
    <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
      {/* Center point where cards appear */}
      <div className="relative">
        {processSteps.map((step, index) => {
          const { translateX } = createCardAnimation(index);

          return (
            <motion.div
              key={step.id}
              className="absolute bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              style={{
                x: translateX,
                width: '280px', // Fixed width for consistent train appearance
                height: '320px', // Fixed height
                left: '-140px', // Center the card (half of width)
                top: '-160px',   // Center the card (half of height)
                zIndex: 100 - index // Lower z-index for later cards so they appear behind
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
