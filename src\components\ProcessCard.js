"use client";

import { motion, useTransform } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Create dynamic train animation where cards appear in center then move left
  const createCardAnimation = (index) => {
    // Cards start appearing after 30% delay to let title stay visible
    const delayOffset = 0.3;

    // Each card appears sequentially
    const cardDelay = (index * 0.13); // Stagger each card by 8% of progress - smaller gap
    const cardFadeStart = delayOffset + cardDelay;
    const cardFadeEnd = cardFadeStart + 0.1; // Quick fade in (10% of progress)
    const cardMoveStart = cardFadeEnd; // Start moving immediately after appearing

    // Opacity: Quick fade in, then stay visible
    const opacity = useTransform(scrollProgress,
      [
        cardFadeStart - 0.02,
        cardFadeEnd,
        1.0 // Stay visible until end
      ],
      [0, 1, 1]
    );

    // Vertical position: Start from below center, move to center during fade
    const translateY = useTransform(scrollProgress,
      [
        cardFadeStart - 0.02,
        cardFadeEnd,
        1.0
      ],
      [50, 0, 0] // Start 50px below, move to center
    );

    // Horizontal movement: Stay in center during fade, then move left at consistent speed
    const translateX = useTransform(scrollProgress, (progress) => {
      // During fade-in, stay at center
      if (progress < cardMoveStart) {
        return 0;
      }

      // After fade-in, move left at a consistent speed for all cards
      // Use the same movement rate regardless of when the card started moving
      const movementProgress = progress - cardMoveStart;
      const pixelsPerProgressUnit = 2400; // Much faster speed to match normal scroll speed

      return -(movementProgress * pixelsPerProgressUnit);
    });

    return { opacity, translateX, translateY };
  };

  return (
    <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
      {/* Center point where cards appear */}
      <div className="relative">
        {processSteps.map((step, index) => {
          const { opacity, translateX, translateY } = createCardAnimation(index);

          return (
            <motion.div
              key={step.id}
              className="absolute bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
              style={{
                opacity,
                x: translateX,
                y: translateY,
                width: '280px', // Fixed width for consistent train appearance
                height: '320px', // Fixed height
                left: '-140px', // Center the card (half of width)
                top: '-160px',   // Center the card (half of height)
                zIndex: 100 - index // Lower z-index for later cards so they appear behind
              }}
            >
              {/* Card Content */}
              <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                {/* Step Number */}
                <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                  <span className="text-primary text-xl font-bold font-heading">
                    {step.number}
                  </span>
                </div>

                {/* Step Title */}
                <h3 className="text-secondary font-heading font-bold text-xl">
                  {step.title}
                </h3>

                {/* Step Description */}
                <p className="text-secondary/80 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};

export default ProcessCard;
