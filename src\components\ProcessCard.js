"use client";

import { motion, useTransform } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  // Simple wrapper movement - move the entire container
  const wrapperTranslateX = useTransform(scrollProgress,
    [0.3, 1.0], // Start moving after 30% delay
    [800, -2000] // Move from right side to left side
  );

  return (
    <div className="w-full h-full flex items-center justify-center relative overflow-hidden">
      {/* Feathered edge masks */}
      <div
        className="absolute inset-0 pointer-events-none z-20"
        style={{
          maskImage: 'linear-gradient(to right, transparent 0%, black 25%, black 75%, transparent 100%)',
          WebkitMaskImage: 'linear-gradient(to right, transparent 0%, black 25%, black 75%, transparent 100%)'
        }}
      >
        {/* Cards wrapper that moves as a unit */}
        <motion.div
          className="relative h-full flex items-center"
          style={{
            x: wrapperTranslateX
          }}
        >
          {processSteps.map((step, index) => {
            const cardSpacing = 350; // pixels between cards
            const leftPosition = index * cardSpacing;

            return (
              <div
                key={step.id}
                className="absolute bg-primary border-2 border-white/20 rounded-2xl p-6 shadow-lg"
                style={{
                  width: '280px',
                  height: '320px',
                  left: `${leftPosition}px`,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 100 - index
                }}
              >
                {/* Card Content */}
                <div className="h-full flex flex-col items-center justify-center text-center space-y-4">
                  {/* Step Number */}
                  <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md">
                    <span className="text-primary text-xl font-bold font-heading">
                      {step.number}
                    </span>
                  </div>

                  {/* Step Title */}
                  <h3 className="text-secondary font-heading font-bold text-xl">
                    {step.title}
                  </h3>

                  {/* Step Description */}
                  <p className="text-secondary/80 text-sm leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            );
          })}
        </motion.div>
      </div>
    </div>
  );
};

export default ProcessCard;
